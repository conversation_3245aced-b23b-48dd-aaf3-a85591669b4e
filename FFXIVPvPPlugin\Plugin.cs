using Dalamud.Game.Command;
using Dalamud.IoC;
using Dalamud.Plugin;
using Dalamud.Interface.Windowing;
using Dalamud.Plugin.Services;
using FFXIVPvPPlugin.Windows;
using FFXIVPvPPlugin.Services;
using FFXIVPvPPlugin.Models;

namespace FFXIVPvPPlugin;

public sealed class Plugin : IDalamudPlugin
{
    [PluginService] internal static IDalamudPluginInterface PluginInterface { get; private set; } = null!;
    [PluginService] internal static ITextureProvider TextureProvider { get; private set; } = null!;
    [PluginService] internal static ICommandManager CommandManager { get; private set; } = null!;
    [PluginService] internal static IClientState ClientState { get; private set; } = null!;
    [PluginService] internal static IDataManager DataManager { get; private set; } = null!;
    [PluginService] internal static IPluginLog Log { get; private set; } = null!;
    [PluginService] internal static IObjectTable ObjectTable { get; private set; } = null!;
    [PluginService] internal static IGameGui GameGui { get; private set; } = null!;
    [PluginService] internal static IFramework Framework { get; private set; } = null!;
    [PluginService] internal static ICondition Condition { get; private set; } = null!;
    [PluginService] internal static ITargetManager TargetManager { get; private set; } = null!;

    private const string CommandName = "/pvp";
    private const string ConfigCommandName = "/pvpconfig";

    public Configuration Configuration { get; init; }
    public readonly WindowSystem WindowSystem = new("FFXIVPvPPlugin");

    // Core Services
    public PvPStateManager PvPStateManager { get; private set; } = null!;
    public EnemyTrackingService EnemyTrackingService { get; private set; } = null!;
    public CooldownTrackingService CooldownTrackingService { get; private set; } = null!;
    public MatchStatisticsService MatchStatisticsService { get; private set; } = null!;
    public PvPModeService PvPModeService { get; private set; } = null!;

    // UI Windows
    private ConfigWindow ConfigWindow { get; init; }
    private MainOverlayWindow MainOverlayWindow { get; init; }
    private EnemyStatusWindow EnemyStatusWindow { get; init; }
    private MatchInfoWindow MatchInfoWindow { get; init; }
    private StatisticsWindow StatisticsWindow { get; init; }

    public Plugin()
    {
        Configuration = PluginInterface.GetPluginConfig() as Configuration ?? new Configuration();

        // Initialize core services
        InitializeServices();

        // Initialize UI windows
        InitializeWindows();

        // Register commands
        RegisterCommands();

        // Hook into UI events
        PluginInterface.UiBuilder.Draw += DrawUI;
        PluginInterface.UiBuilder.OpenConfigUi += ToggleConfigUI;
        PluginInterface.UiBuilder.OpenMainUi += ToggleMainUI;

        Log.Information($"FFXIVPvPPlugin v{PluginInterface.Manifest.AssemblyVersion} loaded successfully!");
    }

    private void InitializeServices()
    {
        PvPStateManager = new PvPStateManager();
        EnemyTrackingService = new EnemyTrackingService(PvPStateManager);
        CooldownTrackingService = new CooldownTrackingService();
        MatchStatisticsService = new MatchStatisticsService();
        PvPModeService = new PvPModeService(PvPStateManager);

        // Start services
        PvPStateManager.Initialize();
        EnemyTrackingService.Initialize();
        CooldownTrackingService.Initialize();
        MatchStatisticsService.Initialize();
        PvPModeService.Initialize();
    }

    private void InitializeWindows()
    {
        ConfigWindow = new ConfigWindow(this);
        MainOverlayWindow = new MainOverlayWindow(this);
        EnemyStatusWindow = new EnemyStatusWindow(this);
        MatchInfoWindow = new MatchInfoWindow(this);
        StatisticsWindow = new StatisticsWindow(this);

        WindowSystem.AddWindow(ConfigWindow);
        WindowSystem.AddWindow(MainOverlayWindow);
        WindowSystem.AddWindow(EnemyStatusWindow);
        WindowSystem.AddWindow(MatchInfoWindow);
        WindowSystem.AddWindow(StatisticsWindow);
    }

    private void RegisterCommands()
    {
        CommandManager.AddHandler(CommandName, new CommandInfo(OnCommand)
        {
            HelpMessage = "Opens the PvP plugin main interface. Use '/pvp config' for settings."
        });

        CommandManager.AddHandler(ConfigCommandName, new CommandInfo(OnConfigCommand)
        {
            HelpMessage = "Opens the PvP plugin configuration window."
        });
    }

    public void Dispose()
    {
        // Dispose services
        PvPModeService?.Dispose();
        MatchStatisticsService?.Dispose();
        CooldownTrackingService?.Dispose();
        EnemyTrackingService?.Dispose();
        PvPStateManager?.Dispose();

        // Dispose UI
        WindowSystem.RemoveAllWindows();
        ConfigWindow?.Dispose();
        MainOverlayWindow?.Dispose();
        EnemyStatusWindow?.Dispose();
        MatchInfoWindow?.Dispose();
        StatisticsWindow?.Dispose();

        // Remove commands
        CommandManager.RemoveHandler(CommandName);
        CommandManager.RemoveHandler(ConfigCommandName);

        Log.Information("FFXIVPvPPlugin disposed successfully.");
    }

    private void OnCommand(string command, string args)
    {
        var arguments = args.Trim().ToLower();
        
        switch (arguments)
        {
            case "config":
                ToggleConfigUI();
                break;
            case "stats":
                ToggleStatisticsUI();
                break;
            case "enemy":
                ToggleEnemyStatusUI();
                break;
            case "match":
                ToggleMatchInfoUI();
                break;
            default:
                ToggleMainUI();
                break;
        }
    }

    private void OnConfigCommand(string command, string args)
    {
        ToggleConfigUI();
    }

    private void DrawUI() => WindowSystem.Draw();

    public void ToggleConfigUI() => ConfigWindow.Toggle();
    public void ToggleMainUI() => MainOverlayWindow.Toggle();
    public void ToggleEnemyStatusUI() => EnemyStatusWindow.Toggle();
    public void ToggleMatchInfoUI() => MatchInfoWindow.Toggle();
    public void ToggleStatisticsUI() => StatisticsWindow.Toggle();
}
