{"Author": "Friend List Monitor", "Name": "Friend List Monitor", "InternalName": "SamplePlugin", "AssemblyVersion": "*******", "Description": "A Dalamud plugin that monitors your friend list for status changes and provides in-game toast notifications when friends come online or go offline. Use /friendmonitor to configure settings.", "ApplicableVersion": "any", "Tags": ["social", "friends", "notifications", "monitoring"], "DalamudApiLevel": 12, "LoadRequiredState": 0, "LoadSync": false, "CanUnloadAsync": false, "LoadPriority": 0, "Punchline": "Monitor friend list status changes and get notifications.", "AcceptsFeedback": true}