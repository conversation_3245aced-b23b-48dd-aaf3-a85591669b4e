﻿using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using ImGuiNET;

namespace SamplePlugin.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;

    // We give this window a constant ID using ###
    // This allows for labels being dynamic, like "{FPS Counter}fps###XYZ counter window",
    // and the window ID will always be "###XYZ counter window" for ImGui
    public ConfigWindow(Plugin plugin) : base("Friend List Monitor Configuration###FriendListMonitorConfig")
    {
        Flags = ImGuiWindowFlags.NoResize | ImGuiWindowFlags.NoCollapse | ImGuiWindowFlags.NoScrollbar |
                ImGuiWindowFlags.NoScrollWithMouse;

        Size = new Vector2(350, 200);
        SizeCondition = ImGuiCond.Always;

        Configuration = plugin.Configuration;
    }

    public void Dispose() { }

    public override void PreDraw()
    {
        // Flags must be added or removed before Draw() is being called, or they won't apply
        if (Configuration.IsConfigWindowMovable)
        {
            Flags &= ~ImGuiWindowFlags.NoMove;
        }
        else
        {
            Flags |= ImGuiWindowFlags.NoMove;
        }
    }

    public override void Draw()
    {
        ImGui.TextUnformatted("Friend List Monitor Settings");
        ImGui.Separator();
        ImGui.Spacing();

        // Plugin enable/disable
        var enablePlugin = Configuration.EnablePlugin;
        if (ImGui.Checkbox("Enable Friend List Monitoring", ref enablePlugin))
        {
            Configuration.EnablePlugin = enablePlugin;
            Configuration.Save();
        }

        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Enable or disable the entire friend list monitoring system");
        }

        ImGui.Spacing();

        // Online notifications
        var enableOnline = Configuration.EnableOnlineNotifications;
        if (ImGui.Checkbox("Show Online Notifications", ref enableOnline))
        {
            Configuration.EnableOnlineNotifications = enableOnline;
            Configuration.Save();
        }

        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Show notifications when friends come online");
        }

        // Offline notifications
        var enableOffline = Configuration.EnableOfflineNotifications;
        if (ImGui.Checkbox("Show Offline Notifications", ref enableOffline))
        {
            Configuration.EnableOfflineNotifications = enableOffline;
            Configuration.Save();
        }

        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Show notifications when friends go offline");
        }

        ImGui.Spacing();
        ImGui.Separator();
        ImGui.Spacing();

        // Window settings
        var movable = Configuration.IsConfigWindowMovable;
        if (ImGui.Checkbox("Movable Config Window", ref movable))
        {
            Configuration.IsConfigWindowMovable = movable;
            Configuration.Save();
        }
    }
}
