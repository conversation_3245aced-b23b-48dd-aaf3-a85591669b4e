{"version": 3, "targets": {"net9.0-windows7.0": {"DalamudPackager/12.0.0": {"type": "package", "build": {"build/DalamudPackager.props": {}, "build/DalamudPackager.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DalamudPackager.props": {}}}, "DotNet.ReproducibleBuilds/1.2.25": {"type": "package", "build": {"buildTransitive/DotNet.ReproducibleBuilds.props": {}, "buildTransitive/DotNet.ReproducibleBuilds.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DotNet.ReproducibleBuilds.props": {}, "buildMultiTargeting/DotNet.ReproducibleBuilds.targets": {}}}}}, "libraries": {"DalamudPackager/12.0.0": {"sha512": "J5TJLV3f16T/E2H2P17ClWjtfEBPpq3yxvqW46eN36JCm6wR+EaoaYkqG9Rm5sHqs3/nK/vKjWWyvEs/jhKoXw==", "type": "package", "path": "dalamudpackager/12.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/DalamudPackager.props", "build/DalamudPackager.targets", "buildMultiTargeting/DalamudPackager.props", "dalamudpackager.12.0.0.nupkg.sha512", "dalamudpackager.nuspec", "tasks/net48/DalamudPackager.dll", "tasks/net48/Newtonsoft.Json.dll", "tasks/net48/YamlDotNet.dll", "tasks/netstandard2.1/DalamudPackager.dll", "tasks/netstandard2.1/Newtonsoft.Json.dll", "tasks/netstandard2.1/YamlDotNet.dll"]}, "DotNet.ReproducibleBuilds/1.2.25": {"sha512": "xCXiw7BCxHJ8pF6wPepRUddlh2dlQlbr81gXA72hdk4FLHkKXas7EH/n+fk5UCA/YfMqG1Z6XaPiUjDbUNBUzg==", "type": "package", "path": "dotnet.reproduciblebuilds/1.2.25", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/DotNet.ReproducibleBuilds.props", "build/DotNet.ReproducibleBuilds.targets", "buildMultiTargeting/DotNet.ReproducibleBuilds.props", "buildMultiTargeting/DotNet.ReproducibleBuilds.targets", "buildTransitive/DotNet.ReproducibleBuilds.props", "buildTransitive/DotNet.ReproducibleBuilds.targets", "dotnet.reproduciblebuilds.1.2.25.nupkg.sha512", "dotnet.reproduciblebuilds.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> >= 12.0.0", "DotNet.ReproducibleBuilds >= 1.2.25"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\ddddd\\SamplePlugin-master\\SamplePlugin\\SamplePlugin.csproj", "projectName": "SamplePlugin", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\ddddd\\SamplePlugin-master\\SamplePlugin\\SamplePlugin.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\ddddd\\SamplePlugin-master\\SamplePlugin\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"DalamudPackager": {"target": "Package", "version": "[12.0.0, )"}, "DotNet.ReproducibleBuilds": {"suppressParent": "All", "target": "Package", "version": "[1.2.25, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}