using System;
using Dalamud.Plugin.Services;

namespace SamplePlugin;

/// <summary>
/// Service for handling friend status notifications
/// </summary>
public class NotificationService : IDisposable
{
    private readonly IToastGui toastGui;
    private readonly IPluginLog log;
    private readonly Configuration configuration;

    public NotificationService(IToastGui toastGui, IPluginLog log, Configuration configuration)
    {
        this.toastGui = toastGui;
        this.log = log;
        this.configuration = configuration;
    }

    /// <summary>
    /// Show a notification for a friend coming online
    /// </summary>
    /// <param name="friendName">Name of the friend</param>
    public void ShowFriendOnlineNotification(string friendName)
    {
        if (!configuration.EnablePlugin || !configuration.EnableOnlineNotifications)
            return;

        try
        {
            var message = $"{friendName} has come online";
            toastGui.ShowNormal(message);
            log.Debug($"Showed online notification for {friendName}");
        }
        catch (Exception ex)
        {
            log.Error(ex, $"Failed to show online notification for {friendName}");
        }
    }

    /// <summary>
    /// Show a notification for a friend going offline
    /// </summary>
    /// <param name="friendName">Name of the friend</param>
    public void ShowFriendOfflineNotification(string friendName)
    {
        if (!configuration.EnablePlugin || !configuration.EnableOfflineNotifications)
            return;

        try
        {
            var message = $"{friendName} has gone offline";
            toastGui.ShowNormal(message);
            log.Debug($"Showed offline notification for {friendName}");
        }
        catch (Exception ex)
        {
            log.Error(ex, $"Failed to show offline notification for {friendName}");
        }
    }

    /// <summary>
    /// Handle friend status change events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    public void OnFriendStatusChanged(object? sender, FriendStatusChangeEventArgs e)
    {
        if (e.WentOnline)
        {
            ShowFriendOnlineNotification(e.Friend.Name);
        }
        else
        {
            ShowFriendOfflineNotification(e.Friend.Name);
        }
    }

    public void Dispose()
    {
        // Nothing to dispose currently
    }
}
