<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Dalamud.NET.Sdk/12.0.2">
  <PropertyGroup>
    <Version>1.0.0</Version>
    <Description>A Dalamud plugin that monitors friend list status changes and provides notifications.</Description>
    <PackageProjectUrl>https://github.com/goatcorp/SamplePlugin</PackageProjectUrl>
    <PackageLicenseExpression>AGPL-3.0-or-later</PackageLicenseExpression>
    <IsPackable>false</IsPackable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="..\Data\goat.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>false</Visible>
    </Content>
  </ItemGroup>
</Project>
