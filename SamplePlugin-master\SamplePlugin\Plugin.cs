﻿using Dalamud.Game.Command;
using Dalamud.IoC;
using Dalamud.Plugin;
using System;
using System.IO;
using Dalamud.Interface.Windowing;
using Dalamud.Plugin.Services;
using SamplePlugin.Windows;

namespace SamplePlugin;

public sealed class Plugin : IDalamudPlugin
{
    [PluginService] internal static IDalamudPluginInterface PluginInterface { get; private set; } = null!;
    [PluginService] internal static ITextureProvider TextureProvider { get; private set; } = null!;
    [PluginService] internal static ICommandManager CommandManager { get; private set; } = null!;
    [PluginService] internal static IClientState ClientState { get; private set; } = null!;
    [PluginService] internal static IDataManager DataManager { get; private set; } = null!;
    [PluginService] internal static IPluginLog Log { get; private set; } = null!;
    [PluginService] internal static IFramework Framework { get; private set; } = null!;
    [PluginService] internal static IToastGui ToastGui { get; private set; } = null!;

    private const string CommandName = "/friendmonitor";

    public Configuration Configuration { get; init; }

    public readonly WindowSystem WindowSystem = new("FriendListMonitor");
    private ConfigWindow ConfigWindow { get; init; }
    private MainWindow MainWindow { get; init; }

    // Friend List Monitor Components
    private FriendListMonitor? friendListMonitor;
    private NotificationService? notificationService;

    public Plugin()
    {
        Configuration = PluginInterface.GetPluginConfig() as Configuration ?? new Configuration();

        // you might normally want to embed resources and load them from the manifest stream
        var goatImagePath = Path.Combine(PluginInterface.AssemblyLocation.Directory?.FullName!, "goat.png");

        ConfigWindow = new ConfigWindow(this);
        MainWindow = new MainWindow(this, goatImagePath);

        WindowSystem.AddWindow(ConfigWindow);
        WindowSystem.AddWindow(MainWindow);

        CommandManager.AddHandler(CommandName, new CommandInfo(OnCommand)
        {
            HelpMessage = "Open Friend List Monitor configuration"
        });

        PluginInterface.UiBuilder.Draw += DrawUI;

        // This adds a button to the plugin installer entry of this plugin which allows
        // to toggle the display status of the configuration ui
        PluginInterface.UiBuilder.OpenConfigUi += ToggleConfigUI;

        // Adds another button that is doing the same but for the main ui of the plugin
        PluginInterface.UiBuilder.OpenMainUi += ToggleMainUI;

        // Initialize Friend List Monitor components
        InitializeFriendListMonitor();

        Log.Information($"Friend List Monitor plugin loaded");
    }

    private void InitializeFriendListMonitor()
    {
        try
        {
            // Initialize notification service
            notificationService = new NotificationService(ToastGui, Log, Configuration);

            // Initialize friend list monitor
            friendListMonitor = new FriendListMonitor(Framework, Log, ClientState);

            // Subscribe to friend status change events
            friendListMonitor.FriendStatusChanged += notificationService.OnFriendStatusChanged;

            Log.Information("Friend List Monitor components initialized successfully");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to initialize Friend List Monitor components");
        }
    }

    public void Dispose()
    {
        // Dispose friend list monitor components
        if (friendListMonitor != null)
        {
            if (notificationService != null)
                friendListMonitor.FriendStatusChanged -= notificationService.OnFriendStatusChanged;
            friendListMonitor.Dispose();
            friendListMonitor = null;
        }

        notificationService?.Dispose();
        notificationService = null;

        WindowSystem.RemoveAllWindows();

        ConfigWindow.Dispose();
        MainWindow.Dispose();

        CommandManager.RemoveHandler(CommandName);

        Log.Information("Friend List Monitor plugin disposed");
    }

    private void OnCommand(string command, string args)
    {
        // in response to the slash command, toggle the configuration UI
        ToggleConfigUI();
    }

    private void DrawUI() => WindowSystem.Draw();

    public void ToggleConfigUI() => ConfigWindow.Toggle();
    public void ToggleMainUI() => MainWindow.Toggle();
}
