﻿using System;
using System.Numerics;
using Dalamud.Interface.Utility;
using Dalamud.Interface.Utility.Raii;
using Dalamud.Interface.Windowing;
using ImGuiNET;
using Lumina.Excel.Sheets;

namespace SamplePlugin.Windows;

public class MainWindow : Window, IDisposable
{
    private string GoatImagePath;
    private Plugin Plugin;

    // We give this window a hidden ID using ##
    // So that the user will see "Friend List Monitor" as window title,
    // but for ImGui the ID is "Friend List Monitor##FriendListMonitorMain"
    public MainWindow(Plugin plugin, string goatImagePath)
        : base("Friend List Monitor##FriendListMonitorMain", ImGuiWindowFlags.NoScrollbar | ImGuiWindowFlags.NoScrollWithMouse)
    {
        SizeConstraints = new WindowSizeConstraints
        {
            MinimumSize = new Vector2(375, 330),
            MaximumSize = new Vector2(float.MaxValue, float.MaxValue)
        };

        GoatImagePath = goatImagePath;
        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        ImGui.TextUnformatted("Friend List Monitor");
        ImGui.Separator();
        ImGui.Spacing();

        // Plugin status
        ImGui.TextUnformatted($"Plugin Status: {(Plugin.Configuration.EnablePlugin ? "Enabled" : "Disabled")}");
        ImGui.TextUnformatted($"Online Notifications: {(Plugin.Configuration.EnableOnlineNotifications ? "Enabled" : "Disabled")}");
        ImGui.TextUnformatted($"Offline Notifications: {(Plugin.Configuration.EnableOfflineNotifications ? "Enabled" : "Disabled")}");

        ImGui.Spacing();

        if (ImGui.Button("Open Settings"))
        {
            Plugin.ToggleConfigUI();
        }

        ImGui.Spacing();
        ImGui.Separator();
        ImGui.Spacing();

        ImGui.TextUnformatted("About Friend List Monitor:");
        ImGui.TextUnformatted("This plugin monitors your friend list for status changes");
        ImGui.TextUnformatted("and shows notifications when friends come online or go offline.");

        ImGui.Spacing();
        ImGui.TextUnformatted("Commands:");
        ImGui.TextUnformatted("/friendmonitor - Open configuration window");

        ImGui.Spacing();

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer != null)
        {
            ImGui.TextUnformatted($"Current Player: {localPlayer.Name}");
            ImGui.TextUnformatted("Friend list monitoring is active while you're logged in.");
        }
        else
        {
            ImGui.TextUnformatted("Please log in to start friend list monitoring.");
        }
    }
}
