{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"SamplePlugin/1.0.0": {"dependencies": {"DalamudPackager": "12.0.0", "DotNet.ReproducibleBuilds": "1.2.25"}, "runtime": {"SamplePlugin.dll": {}}}, "DalamudPackager/12.0.0": {}, "DotNet.ReproducibleBuilds/1.2.25": {}}}, "libraries": {"SamplePlugin/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "DalamudPackager/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-J5TJLV3f16T/E2H2P17ClWjtfEBPpq3yxvqW46eN36JCm6wR+EaoaYkqG9Rm5sHqs3/nK/vKjWWyvEs/jhKoXw==", "path": "dalamudpackager/12.0.0", "hashPath": "dalamudpackager.12.0.0.nupkg.sha512"}, "DotNet.ReproducibleBuilds/1.2.25": {"type": "package", "serviceable": true, "sha512": "sha512-xCXiw7BCxHJ8pF6wPepRUddlh2dlQlbr81gXA72hdk4FLHkKXas7EH/n+fk5UCA/YfMqG1Z6XaPiUjDbUNBUzg==", "path": "dotnet.reproduciblebuilds/1.2.25", "hashPath": "dotnet.reproduciblebuilds.1.2.25.nupkg.sha512"}}}