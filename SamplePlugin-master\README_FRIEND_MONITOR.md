# Friend List Monitor Plugin

A Dalamud plugin for Final Fantasy XIV that monitors friend list status changes and provides in-game notifications.

## Features

### Core Functionality
- **Real-time Friend Monitoring**: Continuously monitors your friend list for status changes
- **Online Notifications**: Shows toast notifications when friends come online
- **Offline Notifications**: Shows toast notifications when friends go offline
- **Configurable Settings**: Enable/disable different types of notifications

### User Interface
- **Configuration Window**: Easy-to-use settings panel accessible via `/friendmonitor` command
- **Main Window**: Status display showing current plugin configuration
- **Toast Notifications**: Non-intrusive in-game notifications with friend names

### Technical Features
- **Lightweight Background Monitoring**: Checks friend list every 5 seconds
- **Proper Error Handling**: Graceful handling of unavailable friend list data
- **Memory Efficient**: Tracks only necessary friend data and status changes
- **Safe Implementation**: Uses proper Dalamud APIs and follows plugin best practices

## Usage

### Installation
1. Place the compiled plugin DLL in your Dalamud dev plugins folder
2. Enable the plugin through the Dalamud plugin installer
3. The plugin will start monitoring automatically when you log in

### Commands
- `/friendmonitor` - Opens the configuration window

### Configuration Options
- **Enable Friend List Monitoring**: Master switch to enable/disable the entire plugin
- **Show Online Notifications**: Toggle notifications for friends coming online
- **Show Offline Notifications**: Toggle notifications for friends going offline

### Notification Format
- Online: "[Friend Name] has come online"
- Offline: "[Friend Name] has gone offline"

## Implementation Details

### Architecture
- **FriendListMonitor**: Core monitoring service that tracks friend status changes
- **NotificationService**: Handles displaying toast notifications
- **Configuration**: Stores user preferences
- **UI Windows**: Configuration and main status windows

### Key Components
1. **FriendData**: Data structure storing friend information and online status
2. **FriendStatusChangeEventArgs**: Event arguments for status change notifications
3. **Periodic Monitoring**: Framework.Update event handler for regular status checks
4. **InfoProxy Integration**: Uses FFXIV client structures to access friend list data

### Safety Features
- Only monitors when player is logged in
- Handles null/invalid friend list data gracefully
- Proper disposal of resources on plugin unload
- Exception handling for all critical operations

## Development Notes

### Dependencies
- Dalamud.NET.Sdk
- FFXIVClientStructs (for friend list access)
- Standard Dalamud plugin services (IToastGui, IFramework, IClientState, etc.)

### Build Requirements
- .NET 8.0
- Unsafe code enabled (for client struct access)
- Dalamud development environment

### File Structure
```
SamplePlugin/
├── Plugin.cs                 # Main plugin class
├── Configuration.cs          # Plugin configuration
├── FriendListMonitor.cs      # Core monitoring logic
├── NotificationService.cs    # Toast notification handling
├── Windows/
│   ├── ConfigWindow.cs       # Configuration UI
│   └── MainWindow.cs         # Main status window
├── SamplePlugin.csproj       # Project file
└── SamplePlugin.json         # Plugin metadata
```

## Testing Checklist

- [ ] Plugin loads without errors
- [ ] Configuration window opens with `/friendmonitor` command
- [ ] Settings are saved and loaded correctly
- [ ] Friend list monitoring starts when logged in
- [ ] Online notifications appear when friends come online
- [ ] Offline notifications appear when friends go offline
- [ ] Notifications respect configuration settings
- [ ] Plugin disposes cleanly when unloaded
- [ ] No memory leaks or performance issues
- [ ] Error handling works for edge cases

## Future Enhancements

Potential improvements for future versions:
- Sound notifications
- Customizable notification messages
- Friend-specific notification settings
- Notification history/log
- Integration with other social features
- Notification timing/cooldown options
