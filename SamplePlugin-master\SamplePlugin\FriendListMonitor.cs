using System;
using System.Collections.Generic;
using System.Linq;
using Dalamud.Plugin.Services;
using Dalamud.Game.Network.Structures.InfoProxy;
using FFXIVClientStructs.FFXIV.Client.UI.Info;

namespace SamplePlugin;

/// <summary>
/// Represents a friend's data and status
/// </summary>
public class FriendData
{
    public string Name { get; set; } = string.Empty;
    public uint ContentId { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastStatusChange { get; set; }

    public FriendData(string name, uint contentId, bool isOnline)
    {
        Name = name;
        ContentId = contentId;
        IsOnline = isOnline;
        LastStatusChange = DateTime.Now;
    }

    public override bool Equals(object? obj)
    {
        return obj is FriendData other && ContentId == other.ContentId;
    }

    public override int GetHashCode()
    {
        return ContentId.GetHashCode();
    }
}

/// <summary>
/// Event arguments for friend status change events
/// </summary>
public class FriendStatusChangeEventArgs : EventArgs
{
    public FriendData Friend { get; }
    public bool WentOnline { get; }

    public FriendStatusChangeEventArgs(FriendData friend, bool wentOnline)
    {
        Friend = friend;
        WentOnline = wentOnline;
    }
}

/// <summary>
/// Monitors friend list for status changes
/// </summary>
public class FriendListMonitor : IDisposable
{
    private readonly IFramework framework;
    private readonly IPluginLog log;
    private readonly IClientState clientState;
    
    private readonly Dictionary<uint, FriendData> trackedFriends = new();
    private DateTime lastUpdate = DateTime.MinValue;
    private readonly TimeSpan updateInterval = TimeSpan.FromSeconds(5); // Check every 5 seconds
    
    public event EventHandler<FriendStatusChangeEventArgs>? FriendStatusChanged;

    public FriendListMonitor(IFramework framework, IPluginLog log, IClientState clientState)
    {
        this.framework = framework;
        this.log = log;
        this.clientState = clientState;
        
        this.framework.Update += OnFrameworkUpdate;
        log.Information("FriendListMonitor initialized");
    }

    private void OnFrameworkUpdate(IFramework framework)
    {
        try
        {
            // Only check if enough time has passed and player is logged in
            if (DateTime.Now - lastUpdate < updateInterval || clientState.LocalPlayer == null)
                return;

            lastUpdate = DateTime.Now;
            CheckFriendListChanges();
        }
        catch (Exception ex)
        {
            log.Error(ex, "Error during friend list update");
        }
    }

    private unsafe void CheckFriendListChanges()
    {
        try
        {
            // Access the friend list through InfoProxy
            var infoModule = InfoModule.Instance();
            if (infoModule == null)
                return;

            var socialInfoProxy = infoModule->GetInfoProxyById(InfoProxyId.SocialList);
            if (socialInfoProxy == null)
                return;

            var friendList = (InfoProxySocialList*)socialInfoProxy;
            if (friendList == null || friendList->CharDataSpan.Length == 0)
                return;

            var currentFriends = new Dictionary<uint, FriendData>();

            // Process current friend list
            for (int i = 0; i < friendList->CharDataSpan.Length; i++)
            {
                var friendInfo = friendList->CharDataSpan[i];
                if (friendInfo.ContentId == 0)
                    continue;

                var name = friendInfo.Name.ToString();
                if (string.IsNullOrEmpty(name))
                    continue;

                var isOnline = friendInfo.OnlineStatus != 0;
                var friend = new FriendData(name, friendInfo.ContentId, isOnline);
                currentFriends[friendInfo.ContentId] = friend;

                // Check for status changes
                if (trackedFriends.TryGetValue(friendInfo.ContentId, out var existingFriend))
                {
                    if (existingFriend.IsOnline != isOnline)
                    {
                        // Status changed
                        friend.LastStatusChange = DateTime.Now;
                        OnFriendStatusChanged(friend, isOnline);
                        log.Debug($"Friend {name} status changed: {(isOnline ? "online" : "offline")}");
                    }
                }
                else
                {
                    // New friend detected (or first time seeing them)
                    log.Debug($"New friend detected: {name} ({(isOnline ? "online" : "offline")})");
                }
            }

            // Update tracked friends
            trackedFriends.Clear();
            foreach (var kvp in currentFriends)
            {
                trackedFriends[kvp.Key] = kvp.Value;
            }
        }
        catch (Exception ex)
        {
            log.Error(ex, "Error checking friend list changes");
        }
    }

    private void OnFriendStatusChanged(FriendData friend, bool wentOnline)
    {
        FriendStatusChanged?.Invoke(this, new FriendStatusChangeEventArgs(friend, wentOnline));
    }

    public IReadOnlyDictionary<uint, FriendData> GetCurrentFriends()
    {
        return trackedFriends.AsReadOnly();
    }

    public void Dispose()
    {
        framework.Update -= OnFrameworkUpdate;
        trackedFriends.Clear();
        log.Information("FriendListMonitor disposed");
    }
}
