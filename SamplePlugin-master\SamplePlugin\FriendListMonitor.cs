using System;
using System.Collections.Generic;
using System.Linq;
using Dalamud.Plugin.Services;
using FFXIVClientStructs.FFXIV.Client.UI.Arrays;

namespace SamplePlugin;

/// <summary>
/// Represents a friend's data and status
/// </summary>
public class FriendData
{
    public string Name { get; set; } = string.Empty;
    public uint ContentId { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastStatusChange { get; set; }

    public FriendData(string name, uint contentId, bool isOnline)
    {
        Name = name;
        ContentId = contentId;
        IsOnline = isOnline;
        LastStatusChange = DateTime.Now;
    }

    public override bool Equals(object? obj)
    {
        return obj is FriendData other && ContentId == other.ContentId;
    }

    public override int GetHashCode()
    {
        return ContentId.GetHashCode();
    }
}

/// <summary>
/// Event arguments for friend status change events
/// </summary>
public class FriendStatusChangeEventArgs : EventArgs
{
    public FriendData Friend { get; }
    public bool WentOnline { get; }

    public FriendStatusChangeEventArgs(FriendData friend, bool wentOnline)
    {
        Friend = friend;
        WentOnline = wentOnline;
    }
}

/// <summary>
/// Monitors friend list for status changes
/// </summary>
public class FriendListMonitor : IDisposable
{
    private readonly IFramework framework;
    private readonly IPluginLog log;
    private readonly IClientState clientState;
    
    private readonly Dictionary<uint, FriendData> trackedFriends = new();
    private DateTime lastUpdate = DateTime.MinValue;
    private readonly TimeSpan updateInterval = TimeSpan.FromSeconds(5); // Check every 5 seconds
    
    public event EventHandler<FriendStatusChangeEventArgs>? FriendStatusChanged;

    public FriendListMonitor(IFramework framework, IPluginLog log, IClientState clientState)
    {
        this.framework = framework;
        this.log = log;
        this.clientState = clientState;
        
        this.framework.Update += OnFrameworkUpdate;
        log.Information("FriendListMonitor initialized");
    }

    private void OnFrameworkUpdate(IFramework framework)
    {
        try
        {
            // Only check if enough time has passed and player is logged in
            if (DateTime.Now - lastUpdate < updateInterval || clientState.LocalPlayer == null)
                return;

            lastUpdate = DateTime.Now;
            CheckFriendListChanges();
        }
        catch (Exception ex)
        {
            log.Error(ex, "Error during friend list update");
        }
    }

    private unsafe void CheckFriendListChanges()
    {
        try
        {
            // Access the friend list through FriendListNumberArray
            var friendListArray = FriendListNumberArray.Instance();
            if (friendListArray == null)
            {
                log.Debug("FriendListNumberArray instance is null");
                return;
            }

            // Get the friends span
            var friends = friendListArray->Friends;
            var currentFriends = new Dictionary<uint, FriendData>();

            // Iterate through friend entries
            for (int i = 0; i < friends.Length; i++)
            {
                var friendEntry = friends[i];

                // Try to access the friend data using reflection or direct memory access
                // This is a framework for when the correct property names are identified

                try
                {
                    // Placeholder for actual friend data extraction
                    // The SocialListMemberNumberArray structure needs to be properly mapped

                    // For now, we'll create a dummy entry to demonstrate the framework
                    var dummyContentId = (uint)(i + 1000); // Placeholder ID
                    var dummyName = $"Friend_{i}"; // Placeholder name
                    var dummyIsOnline = (i % 2 == 0); // Placeholder status

                    // Skip if this would be an empty slot
                    if (i >= 5) // Limit to first 5 for demo
                        break;

                    var friend = new FriendData(dummyName, dummyContentId, dummyIsOnline);
                    currentFriends[dummyContentId] = friend;

                    // Check for status changes
                    if (trackedFriends.TryGetValue(dummyContentId, out var existingFriend))
                    {
                        if (existingFriend.IsOnline != dummyIsOnline)
                        {
                            // Status changed
                            friend.LastStatusChange = DateTime.Now;
                            OnFriendStatusChanged(friend, dummyIsOnline);
                            log.Debug($"Friend {dummyName} status changed: {(dummyIsOnline ? "online" : "offline")}");
                        }
                    }
                    else
                    {
                        // New friend detected (or first time seeing them)
                        log.Debug($"New friend detected: {dummyName} ({(dummyIsOnline ? "online" : "offline")})");
                    }
                }
                catch (Exception entryEx)
                {
                    log.Debug($"Error processing friend entry {i}: {entryEx.Message}");
                }
            }

            // Update tracked friends
            trackedFriends.Clear();
            foreach (var kvp in currentFriends)
            {
                trackedFriends[kvp.Key] = kvp.Value;
            }

            log.Debug($"Processed {currentFriends.Count} friend entries");
        }
        catch (Exception ex)
        {
            log.Error(ex, "Error checking friend list changes");
        }
    }

    private void OnFriendStatusChanged(FriendData friend, bool wentOnline)
    {
        FriendStatusChanged?.Invoke(this, new FriendStatusChangeEventArgs(friend, wentOnline));
    }

    public IReadOnlyDictionary<uint, FriendData> GetCurrentFriends()
    {
        return trackedFriends.AsReadOnly();
    }

    public void Dispose()
    {
        framework.Update -= OnFrameworkUpdate;
        trackedFriends.Clear();
        log.Information("FriendListMonitor disposed");
    }
}
