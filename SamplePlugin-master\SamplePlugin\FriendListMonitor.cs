using System;
using System.Collections.Generic;
using System.Linq;
using Dalamud.Plugin.Services;

namespace SamplePlugin;

/// <summary>
/// Represents a friend's data and status
/// </summary>
public class FriendData
{
    public string Name { get; set; } = string.Empty;
    public uint ContentId { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastStatusChange { get; set; }

    public FriendData(string name, uint contentId, bool isOnline)
    {
        Name = name;
        ContentId = contentId;
        IsOnline = isOnline;
        LastStatusChange = DateTime.Now;
    }

    public override bool Equals(object? obj)
    {
        return obj is FriendData other && ContentId == other.ContentId;
    }

    public override int GetHashCode()
    {
        return ContentId.GetHashCode();
    }
}

/// <summary>
/// Event arguments for friend status change events
/// </summary>
public class FriendStatusChangeEventArgs : EventArgs
{
    public FriendData Friend { get; }
    public bool WentOnline { get; }

    public FriendStatusChangeEventArgs(FriendData friend, bool wentOnline)
    {
        Friend = friend;
        WentOnline = wentOnline;
    }
}

/// <summary>
/// Monitors friend list for status changes
/// </summary>
public class FriendListMonitor : IDisposable
{
    private readonly IFramework framework;
    private readonly IPluginLog log;
    private readonly IClientState clientState;
    
    private readonly Dictionary<uint, FriendData> trackedFriends = new();
    private DateTime lastUpdate = DateTime.MinValue;
    private readonly TimeSpan updateInterval = TimeSpan.FromSeconds(5); // Check every 5 seconds
    
    public event EventHandler<FriendStatusChangeEventArgs>? FriendStatusChanged;

    public FriendListMonitor(IFramework framework, IPluginLog log, IClientState clientState)
    {
        this.framework = framework;
        this.log = log;
        this.clientState = clientState;
        
        this.framework.Update += OnFrameworkUpdate;
        log.Information("FriendListMonitor initialized");
    }

    private void OnFrameworkUpdate(IFramework framework)
    {
        try
        {
            // Only check if enough time has passed and player is logged in
            if (DateTime.Now - lastUpdate < updateInterval || clientState.LocalPlayer == null)
                return;

            lastUpdate = DateTime.Now;
            CheckFriendListChanges();
        }
        catch (Exception ex)
        {
            log.Error(ex, "Error during friend list update");
        }
    }

    private unsafe void CheckFriendListChanges()
    {
        try
        {
            // For now, we'll use a simpler approach that doesn't rely on specific InfoProxy types
            // This is a placeholder implementation that can be expanded when the correct API is available

            // TODO: Implement proper friend list access when InfoProxy API is clarified
            // For demonstration purposes, we'll simulate friend status changes

            log.Debug("Friend list check performed (placeholder implementation)");

            // This would be replaced with actual friend list access code
            // when the correct Dalamud/FFXIVClientStructs API is available
        }
        catch (Exception ex)
        {
            log.Error(ex, "Error checking friend list changes");
        }
    }

    private void OnFriendStatusChanged(FriendData friend, bool wentOnline)
    {
        FriendStatusChanged?.Invoke(this, new FriendStatusChangeEventArgs(friend, wentOnline));
    }

    public IReadOnlyDictionary<uint, FriendData> GetCurrentFriends()
    {
        return trackedFriends.AsReadOnly();
    }

    public void Dispose()
    {
        framework.Update -= OnFrameworkUpdate;
        trackedFriends.Clear();
        log.Information("FriendListMonitor disposed");
    }
}
